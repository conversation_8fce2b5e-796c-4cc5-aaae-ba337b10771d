<template>
  <div class="realtime-monitor-container">
    <div class="monitor-header" v-if="title && title.length > 0">
      <h3 class="monitor-title">{{ title }}</h3>

      <!-- <div class="connection-status">
        <el-icon
          :class="['status-icon', connectionStatus]"
          :color="getStatusColor()"
        >
          <Connection v-if="connectionStatus === 'connected'" />
          <Warning v-else-if="connectionStatus === 'error'" />
          <Loading v-else class="is-loading" />
        </el-icon>
        <span class="status-text">{{ getStatusText() }}</span>
      </div> -->
    </div>

    <div class="monitor-content">
      <div class="data-display">
        <!-- 湿度值显示 -->
        <div class="main-value">
          <div class="top-box">
            <div class="iconfont icon" :class="[iconName]"></div>
            <h4 v-if="subTitle" class="sub-title">{{ subTitle }}</h4>
          </div>
          <div class="value-box">
            <span class="value">{{ formatValue(pointDataVal.humidity) }}</span>
            <span class="unit">%RH</span>
          </div>
        </div>
      </div>

      <!-- 其余三个数据项（流量、压力、温度）-->
      <div class="statistics">
        <div class="stat-item">
          <span class="stat-label">Flow</span>
          <span class="stat-value">{{ formatValue(pointDataVal.flow) }}m³/h</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Pressure</span>
          <span class="stat-value">{{ formatValue(pointDataVal.pressure) }}pa</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Temperature</span>
          <span class="stat-value">{{ formatValue(pointDataVal.temperature) }}℃</span>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
      <el-button @click="reconnect" size="small" type="primary">重连</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import {
  connectWebSocket as wsConnect,
  disconnectWebSocket,
  onWebSocketEvent,
  offWebSocketEvent,
  getWebSocketStatus,
  isWebSocketConnected
} from "@/utils/dataService";
import { ElMessage } from "element-plus";
import {
  Connection,
  Warning,
  Loading,
  ArrowUp,
  ArrowDown,
  Minus,
} from "@element-plus/icons-vue";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "实时监测",
  },
  subTitle: {
    type: String,
    default: "",
  },
  iconName: {
    type: String,
    default: "",
  },
  thresholds: {
    type: Object,
    default: () => ({
      warning: { min: 30, max: 80 },
      danger: { min: 20, max: 90 },
    }),
  },
});

const connectionStatus = ref("connecting"); // connecting, connected, error
const currentValue = ref(0);
const trend = ref("stable"); // up, down, stable
const dataStatus = ref("normal"); // normal, warning, danger
const lastUpdate = ref(new Date());
const error = ref("");
const historyData = ref([]);
const maxHistoryPoints = 20;

// 统计数据
const statistics = ref({
  max: 0,
  min: 0,
  avg: 0,
});

// pointDataVal 当前点位值
const pointDataVal = ref({
  humidity: 0,
  temperature: 0,
  flow: 0,
  pressure : 0
});



// 格式化数值
const formatValue = (value) => {
  if (typeof value !== "number") return "--";
  return value.toFixed(1);
};


// 获取点位的主要数据类型
const getPrimaryDataType = () => {
  return  'humidity';
};

// 处理实时数据
const handleRealTimeData = (data) => {
  try {
    let value;
    let trendValue;
    let pointData;

    // 基于id的数据结构
    if (data[`${props.id}`]) {
      pointData = data[`${props.id}`];
      const primaryDataType = getPrimaryDataType();

      if (pointData['data'][primaryDataType]) {
        value = pointData['data'][primaryDataType].value;
        trendValue = pointData['data'][primaryDataType].trend;
      } else {
        throw new Error("数据格式错误");
      }
    } else {
      throw new Error("无法获取数据");
    }

    // 更新当前值
    const previousValue = currentValue.value;
    currentValue.value = value;
    lastUpdate.value = new Date(data.timestamp);

    // 更新趋势
    if (trendValue) {
      trend.value = trendValue;
    } else {
      // 根据数值变化计算趋势
      const diff = value - previousValue;
      if (Math.abs(diff) < 0.1) {
        trend.value = "stable";
      } else if (diff > 0) {
        trend.value = "up";
      } else {
        trend.value = "down";
      }
    }

    // 更新当前点位值
    if (pointData) {
      pointDataVal.value = {
        humidity: pointData['data']['humidity']?.value || 0,
        temperature: pointData['data']['temperature']?.value || 0,
        flow: pointData['data']['flow']?.value || 0,
        pressure: pointData['data']['pressure']?.value || 0
      };
    }

    // 更新状态
    updateDataStatus(value);

    // 添加到历史数据
    historyData.value.push({
      time: data.timestamp,
      value: value,
    });

    // 保持历史数据数量限制
    if (historyData.value.length > maxHistoryPoints) {
      historyData.value.shift();
    }

    // 更新统计信息
    updateStatistics();
  } catch (err) {
    console.error("处理实时数据错误:", err);
    error.value = "数据处理错误: " + err.message;
  }
};

// 更新数据状态
const updateDataStatus = (value) => {
  const { warning, danger } = props.thresholds;

  if (value <= danger.min || value >= danger.max) {
    dataStatus.value = "danger";
  } else if (value <= warning.min || value >= warning.max) {
    dataStatus.value = "warning";
  } else {
    dataStatus.value = "normal";
  }
};

// 更新统计信息
const updateStatistics = () => {
  if (historyData.value.length === 0) return;

  const values = historyData.value.map((item) => item.value);
  statistics.value = {
    max: Math.max(...values),
    min: Math.min(...values),
    avg: values.reduce((sum, val) => sum + val, 0) / values.length,
  };
};

// 连接WebSocket
const connectWebSocket = async () => {
  try {
    connectionStatus.value = "connecting";
    error.value = "";

    await wsConnect();

    connectionStatus.value = "connected";
    // ElMessage.success("实时数据连接成功");
  } catch (err) {
    connectionStatus.value = "error";
    error.value = "连接失败: " + err.message;
    ElMessage.error("实时数据连接失败");
  }
};

// 重连
const reconnect = () => {
  connectWebSocket();
};

// 处理连接事件
const handleConnected = () => {
  connectionStatus.value = "connected";
  error.value = "";
};

// 处理断开连接事件
const handleDisconnected = () => {
  connectionStatus.value = "error";
  error.value = "连接已断开";
};

// 处理重连失败事件
const handleReconnectFailed = () => {
  connectionStatus.value = "error";
  error.value = "重连失败，请手动重试";
};

// 组件挂载
onMounted(() => {
  // 监听WebSocket事件
  onWebSocketEvent("connected", handleConnected);
  onWebSocketEvent("disconnected", handleDisconnected);
  onWebSocketEvent("reconnectFailed", handleReconnectFailed);
  onWebSocketEvent("realTimeData", handleRealTimeData);

  // 连接WebSocket
  connectWebSocket();
});

// 组件卸载
onUnmounted(() => {
  // 移除事件监听
  offWebSocketEvent("connected", handleConnected);
  offWebSocketEvent("disconnected", handleDisconnected);
  offWebSocketEvent("reconnectFailed", handleReconnectFailed);
  offWebSocketEvent("realTimeData", handleRealTimeData);
});
</script>

<style scoped>
.realtime-monitor-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  /* background: rgba(0, 162, 255, 0.1); */
  /* border-bottom: 1px solid rgba(0, 162, 255, 0.2); */
}

.monitor-title {
  color: #00a2ff;
  font-size: 24px;
  font-weight: 600;
  margin: 10px auto;
}
.sub-title {
  color: #00a2ff;
  font-size: 24px;
  font-weight: 500;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  color: #ffffff;
}

.monitor-content {
  flex: 1;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.data-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.main-value {
  position: relative;
  text-align: center;
}
.main-value .icon {
  font-size: 32px;
  color: #fff;
}
.top-box {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 8px;
  margin-bottom: 4px;
}
.value {
  font-size: 38px;
  font-weight: bold;
  color: #00a2ff;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
}

.unit {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.8;
}

.trend-indicator {
  position: absolute;
  top: -8px;
  right: -20px;
}

.trend-icon {
  font-size: 16px;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.normal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-badge.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-badge.danger {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.last-update {
  font-size: 10px;
  color: #ffffff;
  opacity: 0.6;
}

.statistics {
  display: flex;
  justify-content: space-around;
  padding: 15px 0;
  /* border-top: 1px solid rgba(0, 162, 255, 0.2); */
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.6;
  font-weight: bold;
  margin: 5px auto;
}

.stat-value {
  font-size: 16px;
  color: #00a2ff;
  font-weight: 600;
}

.error-message {
  padding: 12px 16px;
  background: rgba(255, 107, 107, 0.1);
  border-top: 1px solid rgba(255, 107, 107, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  font-size: 12px;
}

.error-message .el-icon {
  font-size: 14px;
}

.error-message .el-button {
  margin-left: auto;
}
</style>
