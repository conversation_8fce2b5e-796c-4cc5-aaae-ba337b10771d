/**
 * 监测数据相关 API 接口
 */

import { get, post } from '@/utils/request'

/**
 * 获取历史监测数据
 * @param {string} type - 传感器类型 (air, exhaust)
 * @param {string} dataType - 数据类型 (humidity, pressure, temperature, flow)
 * @param {number} hours - 时间范围（小时）
 * @param {string} pointId - 点位ID (可选)
 */
export const getHistoryData = (type, dataType, hours = 24, pointId = null) => {
  return get('/monitor/history', {
    type,
    dataType,
    hours,
    pointId
  })
}

/**
 * 获取实时数据快照
 * @param {string} pointId - 点位ID (可选，不传则获取所有点位)
 */
export const getRealTimeSnapshot = (pointId = null) => {
  return get('/monitor/realtime', {
    pointId
  })
}

/**
 * 获取指定点位的详细数据
 * @param {string} pointId - 点位ID
 * @param {string} dataType - 数据类型 (可选)
 */
export const getPointData = (pointId, dataType = null) => {
  return get(`/monitor/point/${pointId}`, {
    dataType
  })
}

/**
 * 获取监测点位列表
 */
export const getMonitorPoints = () => {
  return get('/monitor/points')
}

/**
 * 获取数据统计信息
 * @param {string} pointId - 点位ID
 * @param {string} dataType - 数据类型
 * @param {string} period - 统计周期 (hour, day, week, month)
 */
export const getDataStatistics = (pointId, dataType, period = 'day') => {
  return get('/monitor/statistics', {
    pointId,
    dataType,
    period
  })
}

/**
 * 获取阈值配置
 * @param {string} pointId - 点位ID (可选)
 * @param {string} dataType - 数据类型 (可选)
 */
export const getThresholds = (pointId = null, dataType = null) => {
  return get('/monitor/thresholds', {
    pointId,
    dataType
  })
}

/**
 * 更新阈值配置
 * @param {string} pointId - 点位ID
 * @param {string} dataType - 数据类型
 * @param {object} thresholds - 阈值配置
 */
export const updateThresholds = (pointId, dataType, thresholds) => {
  return post('/monitor/thresholds', {
    pointId,
    dataType,
    thresholds
  })
}

/**
 * 获取报警记录
 * @param {object} params - 查询参数
 */
export const getAlarmRecords = (params = {}) => {
  return get('/monitor/alarms', params)
}

/**
 * 确认报警
 * @param {string} alarmId - 报警ID
 * @param {string} remark - 备注
 */
export const confirmAlarm = (alarmId, remark = '') => {
  return post(`/monitor/alarms/${alarmId}/confirm`, {
    remark
  })
}

/**
 * 导出监测数据
 * @param {object} params - 导出参数
 */
export const exportMonitorData = (params) => {
  return get('/monitor/export', params, {
    responseType: 'blob'
  })
}
