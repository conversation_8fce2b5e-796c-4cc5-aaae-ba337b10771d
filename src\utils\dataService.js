/**
 * 数据服务管理器
 * 根据环境配置自动切换 Mock 数据和真实 API 数据
 */

import { mockHttpApi, mockWebSocket } from './mockData'
import * as api from '@/api'
import { WebSocketClient, WS_EVENTS } from './websocket'

/**
 * 数据源类型枚举
 */
export const DATA_SOURCE = {
  MOCK: 'mock',
  API: 'api'
}

/**
 * 获取当前数据源类型
 */
export const getDataSource = () => {
  return import.meta.env.VITE_DATA_SOURCE || DATA_SOURCE.MOCK
}

/**
 * 是否使用 Mock 数据
 */
export const isMockMode = () => {
  return getDataSource() === DATA_SOURCE.MOCK
}

/**
 * HTTP 数据服务类
 */
class HttpDataService {
  constructor() {
    this.dataSource = getDataSource()
  }
  
  /**
   * 获取历史监测数据
   */
  async getHistoryData(type, dataType, hours = 24, pointId = null) {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      var data = await mockHttpApi.getHistoryData(type, dataType, hours);
      console.log("res data is :",data)
      return data;
    } else {
      return await api.getHistoryData(type, dataType, hours, pointId)
    }
  }
  
  /**
   * 获取实时数据快照
   */
  async getRealTimeSnapshot(pointId = null) {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      return await mockHttpApi.getRealTimeSnapshot()
    } else {
      return await api.getRealTimeSnapshot(pointId)
    }
  }
  
  /**
   * 获取设备状态
   */
  async getDeviceStatus(params = {}) {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      return await mockHttpApi.getDeviceStatus()
    } else {
      return await api.getDeviceStatus(params)
    }
  }
  
  /**
   * 获取指定点位的详细数据
   */
  async getPointData(pointId, dataType = null) {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      // Mock 模式下模拟点位数据
      const snapshot = await mockHttpApi.getRealTimeSnapshot()
      const pointKey = `point${pointId}`
      if (snapshot.success && snapshot.data[pointKey]) {
        return {
          success: true,
          data: snapshot.data[pointKey],
          message: '获取点位数据成功'
        }
      }
      throw new Error('点位数据不存在')
    } else {
      return await api.getPointData(pointId, dataType)
    }
  }
  
  /**
   * 获取监测点位列表
   */
  async getMonitorPoints() {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      // Mock 模式下返回固定的点位列表
      return {
        success: true,
        data: [
          { id: '1', title: 'Exhaust Sleeve（No. 49）', type: 'exhaust' },
          { id: '2', title: 'Injection Sleeve（No. 57）', type: 'injection' },
          { id: '3', title: 'Plant Room（No. 61）', type: 'plant' },
          { id: '4', title: 'Exhaust Sleeve（No. 66）', type: 'exhaust' }
        ],
        message: '获取监测点位成功'
      }
    } else {
      return await api.getMonitorPoints()
    }
  }
  
  /**
   * 获取数据统计信息
   */
  async getDataStatistics(pointId, dataType, period = 'day') {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      // Mock 模式下生成模拟统计数据
      const historyData = await this.getHistoryData('air', dataType, 24)
      if (historyData.success) {
        const values = historyData.data.map(item => item.value)
        return {
          success: true,
          data: {
            max: Math.max(...values),
            min: Math.min(...values),
            avg: values.reduce((sum, val) => sum + val, 0) / values.length,
            count: values.length
          },
          message: '获取统计数据成功'
        }
      }
      throw new Error('获取统计数据失败')
    } else {
      return await api.getDataStatistics(pointId, dataType, period)
    }
  }
  
  /**
   * 获取阈值配置
   */
  async getThresholds(pointId = null, dataType = null) {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      // Mock 模式下返回默认阈值
      return {
        success: true,
        data: {
          humidity: { warning: { min: 30, max: 80 }, danger: { min: 20, max: 90 } },
          pressure: { warning: { min: 1005, max: 1020 }, danger: { min: 1000, max: 1025 } },
          temperature: { warning: { min: 18, max: 28 }, danger: { min: 15, max: 32 } },
          flow: { warning: { min: 100, max: 150 }, danger: { min: 80, max: 180 } }
        },
        message: '获取阈值配置成功'
      }
    } else {
      return await api.getThresholds(pointId, dataType)
    }
  }
  
  /**
   * 用户登录
   */
  async login(username, password) {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      // Mock 模式下模拟登录
      if (username === 'admin' && password === '123456') {
        return {
          success: true,
          data: {
            token: 'mock-token-' + Date.now(),
            refreshToken: 'mock-refresh-token-' + Date.now(),
            userInfo: {
              id: '1',
              username: 'admin',
              name: '管理员',
              role: 'admin',
              permissions: ['*']
            }
          },
          message: '登录成功'
        }
      }
      throw new Error('用户名或密码错误')
    } else {
      return await api.login(username, password)
    }
  }
  
  /**
   * 获取用户信息
   */
  async getUserInfo() {
    if (this.dataSource === DATA_SOURCE.MOCK) {
      return {
        success: true,
        data: {
          id: '1',
          username: 'admin',
          name: '管理员',
          role: 'admin',
          permissions: ['*']
        },
        message: '获取用户信息成功'
      }
    } else {
      return await api.getUserInfo()
    }
  }
}

/**
 * WebSocket 数据服务类（单例模式）
 */
class WebSocketDataService {
  constructor() {
    this.dataSource = getDataSource()
    this.client = null
    this.listeners = new Map()
    this.isConnecting = false
    this.connectionPromise = null
  }

  /**
   * 连接 WebSocket
   */
  async connect() {
    // 如果已经在连接中，返回连接 Promise
    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise
    }

    // 如果已经连接，直接返回
    if (this.client && this.client.isConnected && this.client.isConnected()) {
      return Promise.resolve()
    }

    this.isConnecting = true

    this.connectionPromise = (async () => {
      try {
        if (this.dataSource === DATA_SOURCE.MOCK) {
          // 使用 Mock WebSocket
          if (!this.client) {
            this.client = mockWebSocket
          }
          await this.client.connect()
        } else {
          // 使用真实 WebSocket
          if (!this.client) {
            this.client = new WebSocketClient()
          }
          await this.client.connect()
        }

        // 重新绑定所有事件监听器
        this.rebindListeners()

      } finally {
        this.isConnecting = false
        this.connectionPromise = null
      }
    })()

    return this.connectionPromise
  }
  
  /**
   * 断开连接
   */
  disconnect() {
    if (this.client) {
      this.client.disconnect()
    }
  }
  
  /**
   * 发送消息
   */
  send(data) {
    if (this.client) {
      return this.client.send(data)
    }
    return false
  }
  
  /**
   * 添加事件监听器
   */
  on(event, callback) {
    // 保存监听器，用于重连时重新绑定
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }

    // 检查是否已经存在相同的回调函数
    const callbacks = this.listeners.get(event)
    if (!callbacks.includes(callback)) {
      callbacks.push(callback)

      if (import.meta.env.VITE_DEBUG === 'true') {
        console.log(`[WebSocketDataService] 添加事件监听器: ${event}, 当前监听器数量: ${callbacks.length}`)
      }
    }

    // 如果客户端已连接，立即绑定事件
    if (this.client) {
      this.client.on(event, callback)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }

    // 从客户端移除事件监听器
    if (this.client) {
      this.client.off(event, callback)
    }
  }
  
  /**
   * 获取连接状态
   */
  getStatus() {
    return this.client ? this.client.getStatus() : 'disconnected'
  }
  
  /**
   * 是否已连接
   */
  isConnected() {
    return this.client ? this.client.isConnected() : false
  }
  
  /**
   * 重新绑定事件监听器
   */
  rebindListeners() {
    if (this.client) {
      this.listeners.forEach((callbacks, event) => {
        callbacks.forEach(callback => {
          this.client.on(event, callback)
        })
      })
    }
  }

  /**
   * 清理所有事件监听器
   */
  clearAllListeners() {
    if (this.client) {
      this.listeners.forEach((callbacks, event) => {
        callbacks.forEach(callback => {
          this.client.off(event, callback)
        })
      })
    }
    this.listeners.clear()
  }
}

// 创建全局实例（单例模式）
export const httpDataService = new HttpDataService()

// WebSocket 服务单例
let wsDataServiceInstance = null
export const wsDataService = (() => {
  if (!wsDataServiceInstance) {
    wsDataServiceInstance = new WebSocketDataService()
  }
  return wsDataServiceInstance
})()

// 导出便捷方法
export const getHistoryData = (...args) => httpDataService.getHistoryData(...args)
export const getRealTimeSnapshot = (...args) => httpDataService.getRealTimeSnapshot(...args)
export const getDeviceStatus = (...args) => httpDataService.getDeviceStatus(...args)
export const getPointData = (...args) => httpDataService.getPointData(...args)
export const getMonitorPoints = (...args) => httpDataService.getMonitorPoints(...args)
export const getDataStatistics = (...args) => httpDataService.getDataStatistics(...args)
export const getThresholds = (...args) => httpDataService.getThresholds(...args)
export const login = (...args) => httpDataService.login(...args)
export const getUserInfo = (...args) => httpDataService.getUserInfo(...args)

export const connectWebSocket = () => wsDataService.connect()
export const disconnectWebSocket = () => wsDataService.disconnect()
export const sendWebSocketMessage = (data) => wsDataService.send(data)
export const onWebSocketEvent = (event, callback) => wsDataService.on(event, callback)
export const offWebSocketEvent = (event, callback) => wsDataService.off(event, callback)
export const getWebSocketStatus = () => wsDataService.getStatus()
export const isWebSocketConnected = () => wsDataService.isConnected()

// 调试函数
export const debugWebSocket = () => {
  if (import.meta.env.VITE_DEBUG === 'true') {
    console.group('🔍 WebSocket 调试信息')
    console.log('数据源:', getDataSource())
    console.log('连接状态:', wsDataService.getStatus())
    console.log('客户端实例:', wsDataService.client)
    console.log('监听器数量:', wsDataService.listeners.size)
    wsDataService.listeners.forEach((callbacks, event) => {
      console.log(`事件 ${event}: ${callbacks.length} 个监听器`)
    })
    console.groupEnd()
  }
}

// 在开发模式下暴露到全局
if (import.meta.env.VITE_DEBUG === 'true' && typeof window !== 'undefined') {
  window.debugWebSocket = debugWebSocket
  window.wsDataService = wsDataService
}

export default {
  httpDataService,
  wsDataService,
  getDataSource,
  isMockMode,
  DATA_SOURCE,
  debugWebSocket
}
