/**
 * 应用配置文件
 * 提供环境变量的统一访问和配置管理
 */

// 环境变量配置
export const ENV_CONFIG = {
  // 应用基本信息
  APP_TITLE: import.meta.env.VITE_APP_TITLE || '青马大桥除湿监测系统',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // 环境信息
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  
  // 数据源配置
  DATA_SOURCE: import.meta.env.VITE_DATA_SOURCE || 'mock',
  
  // API 配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1',
  
  // WebSocket 配置
  WS_URL: import.meta.env.VITE_WS_URL || 'ws://localhost:8080/ws',
  
  // 调试模式
  DEBUG: import.meta.env.VITE_DEBUG === 'true'
}

// 数据源类型
export const DATA_SOURCE_TYPES = {
  MOCK: 'mock',
  API: 'api'
}

// 应用配置
export const APP_CONFIG = {
  // 默认阈值配置
  DEFAULT_THRESHOLDS: {
    humidity: {
      warning: { min: 30, max: 80 },
      danger: { min: 20, max: 90 }
    },
    pressure: {
      warning: { min: 1005, max: 1020 },
      danger: { min: 1000, max: 1025 }
    },
    temperature: {
      warning: { min: 18, max: 28 },
      danger: { min: 15, max: 32 }
    },
    flow: {
      warning: { min: 100, max: 150 },
      danger: { min: 80, max: 180 }
    }
  },
  
  // 监测点位配置
  MONITOR_POINTS: [
    {
      id: '1',
      title: 'Exhaust Sleeve（No. 49）',
      primaryDataType: 'humidity', // 主要显示的数据类型
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    },
    {
      id: '2',
      title: 'Injection Sleeve（No. 57）',
      primaryDataType: 'humidity',
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    },
    {
      id: '3',
      title: 'Plant Room（No. 61）',
      primaryDataType: 'humidity',
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    },
    {
      id: '4',
      title: 'Exhaust Sleeve（No. 66）',
      primaryDataType: 'humidity',
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    }
  ],
  
  // 图表配置
  CHART_CONFIG: {
    // 默认时间范围选项
    TIME_RANGES: [
      { label: '最近1小时', value: '1h', hours: 1 },
      { label: '最近6小时', value: '6h', hours: 6 },
      { label: '最近12小时', value: '12h', hours: 12 },
      { label: '最近24小时', value: '24h', hours: 24 },
      { label: '最近3天', value: '3d', hours: 72 },
      { label: '最近7天', value: '7d', hours: 168 }
    ],
    
    // 图表主题色彩
    COLORS: {
      primary: '#00a2ff',
      secondary: '#00ff88',
      warning: '#ffaa00',
      danger: '#ff4757',
      humidity: '#00a2ff',
      pressure: '#ff6b35',
      temperature: '#00ff88',
      flow: '#ffd700'
    }
  },
  
  // WebSocket 配置
  WS_CONFIG: {
    // 重连配置
    RECONNECT_INTERVAL: 3000,
    MAX_RECONNECT_ATTEMPTS: 5,
    
    // 心跳配置
    HEARTBEAT_INTERVAL: 30000,
    
    // 事件类型
    EVENTS: {
      CONNECTED: 'connected',
      DISCONNECTED: 'disconnected',
      ERROR: 'error',
      MESSAGE: 'message',
      REAL_TIME_DATA: 'realTimeData',
      DEVICE_STATUS: 'deviceStatus',
      ALARM: 'alarm',
      RECONNECT_FAILED: 'reconnectFailed'
    }
  },
  
  // 用户配置
  USER_CONFIG: {
    // 默认用户信息
    DEFAULT_USER: {
      username: 'admin',
      password: '123456'
    },
    
    // Token 存储键名
    TOKEN_KEY: 'token',
    REFRESH_TOKEN_KEY: 'refreshToken',
    USER_INFO_KEY: 'userInfo'
  }
}

// 工具函数
export const isProduction = () => ENV_CONFIG.NODE_ENV === 'production'
export const isDevelopment = () => ENV_CONFIG.NODE_ENV === 'development'
export const isMockMode = () => ENV_CONFIG.DATA_SOURCE === DATA_SOURCE_TYPES.MOCK
export const isApiMode = () => ENV_CONFIG.DATA_SOURCE === DATA_SOURCE_TYPES.API
export const isDebugMode = () => ENV_CONFIG.DEBUG

// 获取监测点位信息
export const getMonitorPoint = (id) => {
  return APP_CONFIG.MONITOR_POINTS.find(point => point.id === id)
}

// 获取阈值配置
export const getThresholdConfig = (dataType) => {
  return APP_CONFIG.DEFAULT_THRESHOLDS[dataType] || APP_CONFIG.DEFAULT_THRESHOLDS.humidity
}

// 获取图表颜色
export const getChartColor = (dataType) => {
  return APP_CONFIG.CHART_CONFIG.COLORS[dataType] || APP_CONFIG.CHART_CONFIG.COLORS.primary
}

// 打印配置信息（调试用）
export const printConfig = () => {
  if (isDebugMode()) {
    console.group('🔧 应用配置信息')
    console.log('环境:', ENV_CONFIG.NODE_ENV)
    console.log('数据源:', ENV_CONFIG.DATA_SOURCE)
    console.log('API地址:', ENV_CONFIG.API_BASE_URL)
    console.log('WebSocket地址:', ENV_CONFIG.WS_URL)
    console.log('调试模式:', ENV_CONFIG.DEBUG)
    console.groupEnd()
  }
}

export default {
  ENV_CONFIG,
  APP_CONFIG,
  DATA_SOURCE_TYPES,
  isProduction,
  isDevelopment,
  isMockMode,
  isApiMode,
  isDebugMode,
  getMonitorPoint,
  getThresholdConfig,
  getChartColor,
  printConfig
}
