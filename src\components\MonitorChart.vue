<template>
  <div class="monitor-chart-container">
    <div class="chart-header" v-if="title && title.length > 0">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-controls">
        <el-select
          v-model="timeRange"
          @change="handleTimeRangeChange"
          size="small"
          style="width: 80px"
        >
          <el-option label="1h" value="1h" />
          <el-option label="6h" value="6h" />
          <el-option label="24h" value="24h" />
          <el-option label="7D" value="7d" />
        </el-select>
        <el-button
          @click="toggleRealTime"
          size="small"
          :type="isRealTime ? 'warning' : 'primary'"
        >
          <!-- {{ isRealTime ? "暂停" : "实时" }} -->
          {{ isRealTime ? "pause" : "recover" }}
        </el-button>
      </div>
    </div>

    <div class="chart-content">
      <div
        ref="chartRef"
        class="chart-wrapper"
        :style="{ height: chartHeight + 'px' }"
      ></div>

      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>Loading...</span>
      </div>

      <div v-if="error" class="chart-error">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button @click="retryLoad" size="small" type="primary"
          >重试</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import * as echarts from "echarts";
import { getHistoryData } from "@/utils/dataService";
import { ElMessage } from "element-plus";
import { Loading, Warning } from "@element-plus/icons-vue";

const props = defineProps({
  title: {
    type: String,
    default: "监测图表",
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ["humidity", "pressure"].includes(value),
  },
  sensorType: {
    type: String,
    required: true,
    validator: (value) => ["air", "exhaust"].includes(value),
  },
  chartHeight: {
    type: Number,
    default: 300,
  },
  realTimeEnabled: {
    type: Boolean,
    default: true,
  },
});

const chartRef = ref(null);
const chart = ref(null);
const loading = ref(false);
const error = ref("");
const timeRange = ref("24h");
const isRealTime = ref(props.realTimeEnabled);
const chartData = ref([]);
let realTimeTimer = null;
let resizeTimer = null;

// 安全的图表初始化
const initChart = () => {
  if (!chartRef.value) {
    console.warn("图表容器未准备好");
    return;
  }

  try {
    // 确保容器有有效的尺寸
    if (chartRef.value.offsetWidth === 0 || chartRef.value.offsetHeight === 0) {
      console.warn("图表容器尺寸为0，延迟初始化");
      setTimeout(() => {
        if (
          chartRef.value &&
          chartRef.value.offsetWidth > 0 &&
          chartRef.value.offsetHeight > 0
        ) {
          initChartSafely();
        }
      }, 200);
      return;
    }

    initChartSafely();
  } catch (error) {
    console.error("初始化图表失败:", error);
  }
};

// 安全的图表初始化核心方法
const initChartSafely = () => {
  try {
    // 如果已有图表实例，先销毁
    if (chart.value) {
      chart.value.dispose();
      chart.value = null;
    }

    // 创建新的图表实例
    chart.value = echarts.init(chartRef.value);

    if (!chart.value) {
      console.error("ECharts实例创建失败");
      return;
    }

    // 获取完整的图表配置
    const option = getChartOption();

    // 确保配置完整性
    if (!option || !option.series || !Array.isArray(option.series)) {
      console.error("图表配置不完整");
      return;
    }

    // 设置图表配置
    chart.value.setOption(option, true); // 第二个参数为true，表示不合并配置

    // 添加resize监听器（只添加一次）
    window.removeEventListener("resize", handleResize); // 先移除避免重复
    window.addEventListener("resize", handleResize);

  } catch (error) {
    console.error("安全初始化图表失败:", error);
    if (chart.value) {
      try {
        chart.value.dispose();
      } catch (disposeError) {
        console.error("销毁图表失败:", disposeError);
      }
      chart.value = null;
    }
  }
};

// 获取安全的图表配置
const getChartOption = () => {
  try {
    const seriesName = getSeriesName();
    const lineColor = getLineColor();

    // 确保所有必需的值都存在
    if (!seriesName || !lineColor) {
      console.warn("图表配置参数不完整");
      return null;
    }

    return {
      backgroundColor: "transparent",
      title: {
        show: false,
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        borderColor: "#00a2ff",
        borderWidth: 1,
        textStyle: {
          color: "#ffffff",
        },
        formatter: function (params) {
          if (!params || !params[0]) return "";
          const data = params[0];
          const time = new Date(data.axisValue).toLocaleString();
          const value = data.value;
          const unit = props.dataType === "humidity" ? "%" : "pa";
          return `${time}<br/>${data.seriesName}: ${value}${unit}`;
        },
      },
      legend: {
        show: false,
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "8%",
        top: "8%",
        containLabel: true,
      },
      xAxis: {
        type: "time",
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: "#00a2ff",
          },
        },
        axisLabel: {
          color: "#ffffff",
          fontSize: 10,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(0, 162, 255, 0.1)",
          },
        },
      },
      yAxis: {
        type: "value",
        axisLine: {
          lineStyle: {
            color: "#00a2ff",
          },
        },
        axisLabel: {
          color: "#ffffff",
          fontSize: 10,
          formatter: function (value) {
            const unit = props.dataType === "humidity" ? "%" : "pa";
            return `${value}${unit}`;
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(0, 162, 255, 0.1)",
          },
        },
      },
      series: [
        {
          name: seriesName,
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 4,
          lineStyle: {
            color: lineColor,
            width: 2,
          },
          itemStyle: {
            color: lineColor,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: lineColor + "40" },
              { offset: 1, color: lineColor + "10" },
            ]),
          },
          data: [], // 初始为空数组
        },
      ],
    };
  } catch (error) {
    console.error("生成图表配置失败:", error);
    return null;
  }
};

// 获取系列名称
const getSeriesName = () => {
  const typeMap = {
    air: "进气",
    exhaust: "排气",
  };
  const dataMap = {
    humidity: "湿度",
    pressure: "压力",
  };
  return `${typeMap[props.sensorType]}${dataMap[props.dataType]}`;
};

// 获取线条颜色
const getLineColor = () => {
  if (props.dataType === "humidity") {
    return props.sensorType === "air" ? "#00a2ff" : "#00ff88";
  } else {
    return props.sensorType === "air" ? "#ff6b35" : "#ffd700";
  }
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  error.value = "";

  try {
    const hours = getHoursFromRange(timeRange.value);
    const response = await getHistoryData(
      props.sensorType,
      props.dataType,
      hours
    );

    if (response.success) {
      chartData.value = response.data;
      // 只在图表已初始化时才更新
      if (chart.value) {
        updateChart();
      }
    } else {
      throw new Error(response.message || "获取数据失败");
    }
  } catch (err) {
    error.value = err.message;
    ElMessage.error("加载图表数据失败: " + err.message);
  } finally {
    loading.value = false;
  }
};

// 更新图表
const updateChart = () => {
  if (
    !chart.value ||
    !chartData.value ||
    !Array.isArray(chartData.value) ||
    chartData.value.length === 0
  ) {
    console.warn("图表或数据未准备好");
    return;
  }

  try {
    // 验证数据格式
    const validData = chartData.value.filter(
      (item) =>
        item &&
        item.time &&
        typeof item.value === "number" &&
        !isNaN(item.value)
    );

    if (validData.length === 0) {
      console.warn("没有有效的图表数据");
      // 设置空数据的完整配置
      chart.value.setOption({
        series: [
          {
            name: getSeriesName(),
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 4,
            lineStyle: {
              color: getLineColor(),
              width: 2,
            },
            itemStyle: {
              color: getLineColor(),
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: getLineColor() + "40" },
                { offset: 1, color: getLineColor() + "10" },
              ]),
            },
            data: [],
          },
        ],
      });
      return;
    }

    const data = validData.map((item) => [
      new Date(item.time).getTime(),
      Number(item.value.toFixed(2)),
    ]);

    // 使用完整的配置更新图表，而不是部分更新
    chart.value.setOption({
      series: [
        {
          name: getSeriesName(),
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 4,
          lineStyle: {
            color: getLineColor(),
            width: 2,
          },
          itemStyle: {
            color: getLineColor(),
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: getLineColor() + "40" },
              { offset: 1, color: getLineColor() + "10" },
            ]),
          },
          data: data,
        },
      ],
    }, false); // 第二个参数设为false，表示不合并配置，而是替换
  } catch (error) {
    console.warn("更新图表失败:", error);
  }
};

// 获取时间范围对应的小时数
const getHoursFromRange = (range) => {
  const rangeMap = {
    "1h": 1,
    "6h": 6,
    "24h": 24,
    "7d": 168,
  };
  return rangeMap[range] || 24;
};

// 处理时间范围变化
const handleTimeRangeChange = () => {
  loadData();
};

// 切换实时模式
const toggleRealTime = () => {
  isRealTime.value = !isRealTime.value;

  if (isRealTime.value) {
    startRealTimeUpdate();
  } else {
    stopRealTimeUpdate();
  }
};

// 开始实时更新
const startRealTimeUpdate = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
  }

  realTimeTimer = setInterval(() => {
    loadData();
  }, 5000); // 每5秒更新一次
};

// 停止实时更新
const stopRealTimeUpdate = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
    realTimeTimer = null;
  }
};

// 重试加载
const retryLoad = () => {
  loadData();
};

// 处理窗口大小变化 - 使用更安全的方式
const handleResize = () => {
  // 清除之前的定时器，实现防抖
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  resizeTimer = setTimeout(() => {
    // 完全跳过resize，避免ECharts内部错误
    // 这是一个临时解决方案，直到找到根本原因
    try {
      if (chart.value && typeof chart.value.resize === 'function') {
        // 重新初始化图表而不是resize
        const currentOption = chart.value.getOption();
        if (currentOption && currentOption.series && currentOption.series.length > 0) {
          // 销毁并重新创建图表
          chart.value.dispose();
          chart.value = null;

          nextTick(() => {
            initChart();
            if (chart.value && chartData.value && chartData.value.length > 0) {
              updateChart();
            }
          });
        }
      }
    } catch (error) {
      console.warn("图表resize失败，尝试重新初始化:", error);
      // 如果出错，完全重新初始化
      try {
        if (chart.value) {
          chart.value.dispose();
          chart.value = null;
        }
        nextTick(() => {
          initChart();
          if (chart.value && chartData.value && chartData.value.length > 0) {
            updateChart();
          }
        });
      } catch (reinitError) {
        console.error("重新初始化图表失败:", reinitError);
      }
    }
  }, 300); // 增加延迟时间
};

// 监听属性变化
watch([() => props.dataType, () => props.sensorType], () => {
  try {
    // 停止实时更新
    stopRealTimeUpdate();

    // 移除resize监听器
    window.removeEventListener("resize", handleResize);

    // 销毁现有图表
    if (chart.value && chart.value.dispose) {
      chart.value.dispose();
      chart.value = null;
    }

    nextTick(() => {
      initChart();
      if (chart.value) {
        loadData();
        if (isRealTime.value) {
          startRealTimeUpdate();
        }
      }
    });
  } catch (error) {
    console.warn("重新初始化图表失败:", error);
  }
});

// 组件挂载
onMounted(() => {
  nextTick(() => {
    try {
      // 确保DOM元素已经渲染
      if (!chartRef.value) {
        console.warn("图表容器DOM未准备好，延迟初始化");
        setTimeout(() => {
          if (chartRef.value) {
            initChart();
            if (chart.value) {
              loadData();
              if (isRealTime.value) {
                startRealTimeUpdate();
              }
            }
          }
        }, 100);
        return;
      }

      initChart();
      if (chart.value) {
        loadData();

        if (isRealTime.value) {
          startRealTimeUpdate();
        }
      }
    } catch (error) {
      console.error("组件初始化失败:", error);
      error.value = "图表初始化失败";
    }
  });
});

// 组件卸载
onUnmounted(() => {
  stopRealTimeUpdate();

  try {
    // 清理resize定时器
    if (resizeTimer) {
      clearTimeout(resizeTimer);
      resizeTimer = null;
    }

    // 移除resize监听器
    window.removeEventListener("resize", handleResize);

    // 销毁图表实例
    if (chart.value && chart.value.dispose) {
      chart.value.dispose();
      chart.value = null;
    }
  } catch (error) {
    console.warn("销毁图表失败:", error);
  }
});
</script>

<style scoped>
.monitor-chart-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  /* background: rgba(0, 162, 255, 0.1); */
  /* border-bottom: 1px solid rgba(0, 162, 255, 0.2); */
}

.chart-title {
  color: #00a2ff;
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-wrapper {
  width: 100%;
}

.chart-loading,
.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 14px;
}

.chart-error {
  color: #ff6b6b;
}

.chart-loading .el-icon {
  font-size: 24px;
  color: #00a2ff;
}

.chart-error .el-icon {
  font-size: 24px;
  color: #ff6b6b;
}

/* Element Plus 样式覆盖 */
:deep(.el-select) {
  width: 120px;
}

:deep(.el-select .el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 162, 255, 0.3);
  color: #ffffff;
}

:deep(.el-button--small) {
  padding: 5px 8px;
  font-size: 12px;
}
</style>
