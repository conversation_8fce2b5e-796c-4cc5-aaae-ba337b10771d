<template>
  <div class="monitor-chart-container">
    <div class="chart-header" v-if="title && title.length > 0">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-controls">
        <el-select
          v-model="timeRange"
          @change="handleTimeRangeChange"
          size="small"
          style="width: 80px"
        >
          <el-option label="1h" value="1h" />
          <el-option label="6h" value="6h" />
          <el-option label="24h" value="24h" />
          <el-option label="7D" value="7d" />
        </el-select>
        <el-button
          @click="toggleRealTime"
          size="small"
          :type="isRealTime ? 'warning' : 'primary'"
        >
          <!-- {{ isRealTime ? "暂停" : "实时" }} -->
          {{ isRealTime ? "pause" : "recover" }}
        </el-button>
      </div>
    </div>

    <div class="chart-content">
      <div
        ref="chartRef"
        class="chart-wrapper"
        :style="{ height: chartHeight + 'px' }"
      ></div>

      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <div v-if="error" class="chart-error">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button @click="retryLoad" size="small" type="primary"
          >重试</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import * as echarts from "echarts";
import { getHistoryData } from "@/utils/dataService";
import { ElMessage } from "element-plus";
import { Loading, Warning } from "@element-plus/icons-vue";

const props = defineProps({
  title: {
    type: String,
    default: "监测图表",
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ["humidity", "pressure"].includes(value),
  },
  sensorType: {
    type: String,
    required: true,
    validator: (value) => ["air", "exhaust"].includes(value),
  },
  chartHeight: {
    type: Number,
    default: 300,
  },
  realTimeEnabled: {
    type: Boolean,
    default: true,
  },
});

const chartRef = ref(null);
const chart = ref(null);
const loading = ref(false);
const error = ref("");
const timeRange = ref("24h");
const isRealTime = ref(props.realTimeEnabled);
const chartData = ref([]);
let realTimeTimer = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) {
    console.warn("图表容器未准备好");
    return;
  }

  try {
    // 确保容器有有效的尺寸
    if (chartRef.value.offsetWidth === 0 || chartRef.value.offsetHeight === 0) {
      console.warn("图表容器尺寸为0，延迟初始化");
      setTimeout(() => {
        if (
          chartRef.value &&
          chartRef.value.offsetWidth > 0 &&
          chartRef.value.offsetHeight > 0
        ) {
          chart.value = echarts.init(chartRef.value);
          if (chart.value) {
            chart.value.setOption(getChartOption());
            window.addEventListener("resize", handleResize);
          }
        }
      }, 200);
      return;
    }

    chart.value = echarts.init(chartRef.value);
  } catch (error) {
    console.error("初始化图表失败:", error);
    return;
  }

  const option = getChartOption();
  chart.value.setOption(option);

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
};

// 获取图表配置
const getChartOption = () => {
  return {
    backgroundColor: "transparent",
    title: {
      show: false,
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#00a2ff",
      borderWidth: 1,
      textStyle: {
        color: "#ffffff",
      },
      formatter: function (params) {
        const data = params[0];
        const time = new Date(data.axisValue).toLocaleString();
        const value = data.value;
        const unit = props.dataType === "humidity" ? "%" : "hPa";
        return `${time}<br/>${data.seriesName}: ${value}${unit}`;
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "8%",
      top: "8%",
      containLabel: true,
    },
    xAxis: {
      type: "time",
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: "#00a2ff",
        },
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 10,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(0, 162, 255, 0.1)",
        },
      },
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#00a2ff",
        },
      },
      axisLabel: {
        color: "#ffffff",
        fontSize: 10,
        formatter: function (value) {
          const unit = props.dataType === "humidity" ? "%" : "hPa";
          return `${value}${unit}`;
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(0, 162, 255, 0.1)",
        },
      },
    },
    series: [
      {
        name: getSeriesName(),
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 4,
        lineStyle: {
          color: getLineColor(),
          width: 2,
        },
        itemStyle: {
          color: getLineColor(),
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: getLineColor() + "40" },
            { offset: 1, color: getLineColor() + "10" },
          ]),
        },
        data: [],
      },
    ],
  };
};

// 获取系列名称
const getSeriesName = () => {
  const typeMap = {
    air: "进气",
    exhaust: "排气",
  };
  const dataMap = {
    humidity: "湿度",
    pressure: "压力",
  };
  return `${typeMap[props.sensorType]}${dataMap[props.dataType]}`;
};

// 获取线条颜色
const getLineColor = () => {
  if (props.dataType === "humidity") {
    return props.sensorType === "air" ? "#00a2ff" : "#00ff88";
  } else {
    return props.sensorType === "air" ? "#ff6b35" : "#ffd700";
  }
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  error.value = "";

  try {
    const hours = getHoursFromRange(timeRange.value);
    const response = await getHistoryData(
      props.sensorType,
      props.dataType,
      hours
    );

    if (response.success) {
      chartData.value = response.data;
      // 只在图表已初始化时才更新
      if (chart.value) {
        updateChart();
      }
    } else {
      throw new Error(response.message || "获取数据失败");
    }
  } catch (err) {
    error.value = err.message;
    ElMessage.error("加载图表数据失败: " + err.message);
  } finally {
    loading.value = false;
  }
};

// 更新图表
const updateChart = () => {
  if (
    !chart.value ||
    !chartData.value ||
    !Array.isArray(chartData.value) ||
    chartData.value.length === 0
  ) {
    console.warn("图表或数据未准备好");
    return;
  }

  try {
    // 验证数据格式
    const validData = chartData.value.filter(
      (item) =>
        item &&
        item.time &&
        typeof item.value === "number" &&
        !isNaN(item.value)
    );

    if (validData.length === 0) {
      console.warn("没有有效的图表数据");
      return;
    }

    const data = validData.map((item) => [
      new Date(item.time).getTime(),
      Number(item.value.toFixed(2)),
    ]);

    chart.value.setOption({
      series: [
        {
          name: getSeriesName(),
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 4,
          lineStyle: {
            color: getLineColor(),
            width: 2,
          },
          itemStyle: {
            color: getLineColor(),
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: getLineColor() + "40" },
              { offset: 1, color: getLineColor() + "10" },
            ]),
          },
          data: data,
        },
      ],
    });
  } catch (error) {
    console.warn("更新图表失败:", error);
  }
};

// 获取时间范围对应的小时数
const getHoursFromRange = (range) => {
  const rangeMap = {
    "1h": 1,
    "6h": 6,
    "24h": 24,
    "7d": 168,
  };
  return rangeMap[range] || 24;
};

// 处理时间范围变化
const handleTimeRangeChange = () => {
  loadData();
};

// 切换实时模式
const toggleRealTime = () => {
  isRealTime.value = !isRealTime.value;

  if (isRealTime.value) {
    startRealTimeUpdate();
  } else {
    stopRealTimeUpdate();
  }
};

// 开始实时更新
const startRealTimeUpdate = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
  }

  realTimeTimer = setInterval(() => {
    loadData();
  }, 5000); // 每5秒更新一次
};

// 停止实时更新
const stopRealTimeUpdate = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
    realTimeTimer = null;
  }
};

// 重试加载
const retryLoad = () => {
  loadData();
};

// 处理窗口大小变化
const handleResize = () => {
  if (chart.value && chart.value.resize) {
    try {
      chart.value.resize();
    } catch (error) {
      console.warn("图表resize失败:", error);
    }
  }
};

// 监听属性变化
watch([() => props.dataType, () => props.sensorType], () => {
  try {
    if (chart.value && chart.value.dispose) {
      chart.value.dispose();
    }
    nextTick(() => {
      initChart();
      if (chart.value) {
        loadData();
      }
    });
  } catch (error) {
    console.warn("重新初始化图表失败:", error);
  }
});

// 组件挂载
onMounted(() => {
  nextTick(() => {
    try {
      // 确保DOM元素已经渲染
      if (!chartRef.value) {
        console.warn("图表容器DOM未准备好，延迟初始化");
        setTimeout(() => {
          if (chartRef.value) {
            initChart();
            if (chart.value) {
              loadData();
              if (isRealTime.value) {
                startRealTimeUpdate();
              }
            }
          }
        }, 100);
        return;
      }

      initChart();
      if (chart.value) {
        loadData();

        if (isRealTime.value) {
          startRealTimeUpdate();
        }
      }
    } catch (error) {
      console.error("组件初始化失败:", error);
      error.value = "图表初始化失败";
    }
  });
});

// 组件卸载
onUnmounted(() => {
  stopRealTimeUpdate();

  try {
    if (chart.value && chart.value.dispose) {
      chart.value.dispose();
    }
  } catch (error) {
    console.warn("销毁图表失败:", error);
  }

  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.monitor-chart-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  /* background: rgba(0, 162, 255, 0.1); */
  /* border-bottom: 1px solid rgba(0, 162, 255, 0.2); */
}

.chart-title {
  color: #00a2ff;
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-wrapper {
  width: 100%;
}

.chart-loading,
.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 14px;
}

.chart-error {
  color: #ff6b6b;
}

.chart-loading .el-icon {
  font-size: 24px;
  color: #00a2ff;
}

.chart-error .el-icon {
  font-size: 24px;
  color: #ff6b6b;
}

/* Element Plus 样式覆盖 */
:deep(.el-select) {
  width: 120px;
}

:deep(.el-select .el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 162, 255, 0.3);
  color: #ffffff;
}

:deep(.el-button--small) {
  padding: 5px 8px;
  font-size: 12px;
}
</style>
