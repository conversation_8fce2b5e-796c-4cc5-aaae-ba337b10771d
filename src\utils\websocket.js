/**
 * WebSocket 工具类
 * 使用 SockJS 封装 WebSocket 连接，提供重连、事件管理等功能
 */

import SockJS from 'sockjs-client'
import { ElMessage } from 'element-plus'

/**
 * WebSocket 连接状态枚举
 */
export const WS_STATUS = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
  RECONNECTING: 'reconnecting'
}

/**
 * WebSocket 事件类型枚举
 */
export const WS_EVENTS = {
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  ERROR: 'error',
  MESSAGE: 'message',
  REAL_TIME_DATA: 'realTimeData',
  DEVICE_STATUS: 'deviceStatus',
  ALARM: 'alarm',
  RECONNECT_FAILED: 'reconnectFailed'
}

/**
 * WebSocket 客户端类
 */
export class WebSocketClient {
  constructor(options = {}) {
    this.url = options.url || import.meta.env.VITE_WS_URL
    this.protocols = options.protocols || []
    this.options = {
      // SockJS 配置
      transports: ['websocket', 'xhr-polling'],
      timeout: 10000,
      // 重连配置
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      // 心跳配置
      heartbeat: true,
      heartbeatInterval: 30000,
      // 调试模式
      debug: import.meta.env.VITE_DEBUG === 'true',
      ...options
    }
    
    // 连接实例
    this.socket = null
    
    // 连接状态
    this.status = WS_STATUS.DISCONNECTED
    
    // 事件监听器
    this.listeners = new Map()
    
    // 重连相关
    this.reconnectAttempts = 0
    this.reconnectTimer = null
    
    // 心跳相关
    this.heartbeatTimer = null
    this.lastHeartbeat = null
    
    // 消息队列（连接断开时缓存消息）
    this.messageQueue = []
  }
  
  /**
   * 连接 WebSocket
   */
  connect() {
    return new Promise((resolve, reject) => {
      try {
        if (this.socket && this.status === WS_STATUS.CONNECTED) {
          resolve()
          return
        }
        
        this.status = WS_STATUS.CONNECTING
        this.emit(WS_EVENTS.CONNECTED, { status: this.status })
        
        // 创建 SockJS 连接
        this.socket = new SockJS(this.url, this.protocols, this.options)
        
        // 连接成功
        this.socket.onopen = () => {
          this.status = WS_STATUS.CONNECTED
          this.reconnectAttempts = 0
          
          this.log('WebSocket 连接成功')
          
          // 开始心跳
          if (this.options.heartbeat) {
            this.startHeartbeat()
          }
          
          // 发送缓存的消息
          this.flushMessageQueue()
          
          this.emit(WS_EVENTS.CONNECTED, { status: this.status })
          resolve()
        }
        
        // 接收消息
        this.socket.onmessage = (event) => {
          this.handleMessage(event.data)
        }
        
        // 连接关闭
        this.socket.onclose = (event) => {
          this.status = WS_STATUS.DISCONNECTED
          this.stopHeartbeat()
          
          this.log('WebSocket 连接关闭', event)
          
          this.emit(WS_EVENTS.DISCONNECTED, { 
            status: this.status,
            code: event.code,
            reason: event.reason
          })
          
          // 自动重连
          if (this.options.reconnect && !event.wasClean) {
            this.reconnect()
          }
        }
        
        // 连接错误
        this.socket.onerror = (error) => {
          this.status = WS_STATUS.ERROR
          this.log('WebSocket 连接错误', error)
          
          this.emit(WS_EVENTS.ERROR, { 
            status: this.status,
            error: error
          })
          
          reject(error)
        }
        
      } catch (error) {
        this.status = WS_STATUS.ERROR
        this.log('WebSocket 连接异常', error)
        reject(error)
      }
    })
  }
  
  /**
   * 断开连接
   */
  disconnect() {
    this.options.reconnect = false
    this.stopHeartbeat()
    this.clearReconnectTimer()
    
    if (this.socket) {
      this.socket.close()
      this.socket = null
    }
    
    this.status = WS_STATUS.DISCONNECTED
    this.log('WebSocket 主动断开连接')
  }
  
  /**
   * 重连
   */
  reconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.log('WebSocket 重连失败，已达到最大重试次数')
      this.emit(WS_EVENTS.RECONNECT_FAILED, {
        attempts: this.reconnectAttempts,
        maxAttempts: this.options.maxReconnectAttempts
      })
      return
    }
    
    this.reconnectAttempts++
    this.status = WS_STATUS.RECONNECTING
    
    this.log(`WebSocket 开始第 ${this.reconnectAttempts} 次重连`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(() => {
        this.reconnect()
      })
    }, this.options.reconnectInterval)
  }
  
  /**
   * 发送消息
   */
  send(data) {
    if (this.status !== WS_STATUS.CONNECTED) {
      // 连接未建立时，将消息加入队列
      this.messageQueue.push(data)
      this.log('WebSocket 未连接，消息已加入队列', data)
      return false
    }
    
    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      this.socket.send(message)
      this.log('WebSocket 发送消息', data)
      return true
    } catch (error) {
      this.log('WebSocket 发送消息失败', error)
      return false
    }
  }
  
  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data
      this.log('WebSocket 接收消息', message)
      
      // 处理心跳响应
      if (message.type === 'pong') {
        this.lastHeartbeat = Date.now()
        return
      }
      
      // 触发通用消息事件
      this.emit(WS_EVENTS.MESSAGE, message)
      
      // 根据消息类型触发特定事件
      if (message.type) {
        this.emit(message.type, message.data || message)
      }
      
    } catch (error) {
      this.log('WebSocket 消息解析失败', error)
    }
  }
  
  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.status === WS_STATUS.CONNECTED) {
        this.send({ type: 'ping', timestamp: Date.now() })
      }
    }, this.options.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  /**
   * 清除重连定时器
   */
  clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  /**
   * 发送队列中的消息
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.send(message)
    }
  }
  
  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }
  
  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }
  
  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          this.log('WebSocket 事件处理错误', error)
        }
      })
    }
  }
  
  /**
   * 获取连接状态
   */
  getStatus() {
    return this.status
  }
  
  /**
   * 是否已连接
   */
  isConnected() {
    return this.status === WS_STATUS.CONNECTED
  }
  
  /**
   * 日志输出
   */
  log(message, data = null) {
    if (this.options.debug) {
      console.log(`[WebSocket] ${message}`, data || '')
    }
  }
  
  /**
   * 销毁实例
   */
  destroy() {
    this.disconnect()
    this.listeners.clear()
    this.messageQueue = []
  }
}

// 创建默认实例
export const defaultWebSocket = new WebSocketClient()

// 导出默认实例的方法
export const connect = () => defaultWebSocket.connect()
export const disconnect = () => defaultWebSocket.disconnect()
export const send = (data) => defaultWebSocket.send(data)
export const on = (event, callback) => defaultWebSocket.on(event, callback)
export const off = (event, callback) => defaultWebSocket.off(event, callback)
export const getStatus = () => defaultWebSocket.getStatus()
export const isConnected = () => defaultWebSocket.isConnected()

export default WebSocketClient
